import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Image,
  Animated,
  Easing,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { AppImages } from "../../../utils/AppImages";
import RazorpayCheckout from "react-native-razorpay";
import { useSelector, useDispatch } from "react-redux";
import { createPaymentOrder, verifyPayment, dismissPayment } from "../../../api/Payment";
import { clearCart } from "../../../redux/slices/cartSlice";
import { getRazorpayConfig } from "../../../config/razorpay";

function PersonalDetailsPopup({ firstModalVisible, setFirstModalVisible, navigation }) {
  const [activeInput, setActiveInput] = useState(null);
  const [successPopupVisible, setSuccessPopupVisible] = useState(false);
  const [slideAnim] = useState(new Animated.Value(300));
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentOrderId, setPaymentOrderId] = useState(null);

  // Form state
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");

  // Redux state
  const checkoutData = useSelector((state) => state.cart.checkoutData);
  const dispatch = useDispatch();

  useEffect(() => {
    if (firstModalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    } else {
      slideAnim.setValue(300);
    }
  }, [firstModalVisible]);

  const handleSave = async () => {
    // Validate form fields
    if (!fullName.trim() || !email.trim() || !phoneNumber.trim()) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    if (!checkoutData?.checkout_id) {
      Alert.alert("Error", "No checkout data found. Please try again.");
      return;
    }

    setIsProcessingPayment(true);

    try {
      // Create payment order
      console.log("🔄 Creating payment order...");
      const paymentOrderResponse = await createPaymentOrder(checkoutData.checkout_id);

      // Your API returns data directly, not wrapped in success/data structure
      if (!paymentOrderResponse || !paymentOrderResponse.order_id) {
        throw new Error("Failed to create payment order - missing order_id");
      }

      // Map your API response fields to expected variables
      const {
        order_id: razorpay_order_id,
        amount,
        // Use order_id as payment_order_id since that's what your API provides
        order_id: payment_order_id
      } = paymentOrderResponse;

      setPaymentOrderId(payment_order_id);

      // Get Razorpay configuration
      const razorpayConfig = getRazorpayConfig();

      // Configure Razorpay options
      const options = {
        description: razorpayConfig.PAYMENT_DESCRIPTION,
        image: razorpayConfig.COMPANY_LOGO,
        currency: razorpayConfig.CURRENCY,
        key: razorpayConfig.KEY_ID,
        amount: amount * 100, // Amount in paise
        order_id: razorpay_order_id,
        name: razorpayConfig.COMPANY_NAME,
        prefill: {
          email: email,
          contact: phoneNumber,
          name: fullName
        },
        theme: { color: razorpayConfig.THEME_COLOR }
      };

      console.log("🔄 Opening Razorpay checkout...");

      // Open Razorpay checkout
      RazorpayCheckout.open(options)
        .then(async (data) => {
          console.log("✅ Payment successful:", data);

          try {
            // Verify payment - your API only expects razorpay_order_id
            const verificationResponse = await verifyPayment(
              data.razorpay_order_id
            );

            // Your API returns data directly, check if verification was successful
            console.log("🔍 Verification response:", verificationResponse);

            if (verificationResponse && verificationResponse.status === "payment.order_created") {
              console.log("✅ Payment verified successfully");
              // Clear cart and navigate to success screen
              dispatch(clearCart());
              setFirstModalVisible(false); // Close the popup

              // Navigate to payment success screen with order data
              console.log("🚀 Navigating to PaymentResult with success status");
              navigation.navigate("PaymentResult", {
                paymentStatus: "success",
                orderData: {
                  orderCode: verificationResponse?.order_code || "ADF27",
                  restaurant: {
                    name: "Flywheel Restaurant", // This should come from your order data
                    location: "Kesavadasapuram",
                    distance: "3 Km",
                    logo: null
                  },
                  pickupWindow: "6:30-8:30 pm, Today",
                  orderId: verificationResponse?.order_id || razorpay_order_id,
                  items: "1x Meal Bag", // This should come from cart data
                  total: `Rs ${amount}`,
                  paymentMethod: "UPI",
                  orderStatus: "Processing",
                  pickupCountdown: "01h:23m:45s"
                }
              });
            } else {
              console.log("❌ Payment verification failed - unexpected response:", verificationResponse);
              throw new Error("Payment verification failed - unexpected response format");
            }
          } catch (verifyError) {
            console.error("❌ Payment verification error:", verifyError);
            setFirstModalVisible(false); // Close the popup

            // Navigate to payment failed screen
            console.log("🚀 Navigating to PaymentResult with failed status");
            navigation.navigate("PaymentResult", {
              paymentStatus: "failed"
            });
          }
        })
        .catch(async (error) => {
          console.error("❌ Payment failed:", error);

          // Dismiss the payment order if payment failed
          if (payment_order_id) {
            try {
              await dismissPayment(payment_order_id);
              console.log("✅ Payment order dismissed");
            } catch (dismissError) {
              console.error("❌ Error dismissing payment:", dismissError);
            }
          }

          setFirstModalVisible(false); // Close the popup

          if (error.code === RazorpayCheckout.PAYMENT_CANCELLED) {
            // For cancelled payments, just go back without showing error screen
            console.log("Payment cancelled by user");
          } else {
            // Navigate to payment failed screen for actual failures
            navigation.navigate("PaymentResult", {
              paymentStatus: "failed"
            });
          }
        });

    } catch (error) {
      console.error("❌ Error in payment process:", error);
      setFirstModalVisible(false); // Close the popup

      // Navigate to payment failed screen
      navigation.navigate("PaymentResult", {
        paymentStatus: "failed"
      });
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handleCloseSuccessPopup = () => {
    setSuccessPopupVisible(false);
    setFirstModalVisible(false);
    // Reset form
    setFullName("");
    setEmail("");
    setPhoneNumber("");
    setPaymentOrderId(null);
  };

  const renderInputField = (label, value, onChangeText, keyboardType) => (
    <View style={styles.inputGroup}>
      <Text style={styles.label}>{label}</Text>
      <TextInput
        style={[
          styles.input,
          activeInput === label.toLowerCase().replace(" ", "") &&
            styles.activeInput,
        ]}
        placeholder={label}
        value={value}
        onChangeText={onChangeText}
        keyboardType={keyboardType}
        placeholderTextColor="#999"
        onFocus={() => setActiveInput(label.toLowerCase().replace(" ", ""))}
        onBlur={() => setActiveInput(null)}
        editable={!isProcessingPayment}
      />
    </View>
  );

  return (
    <>
      <Modal
        animationType="none"
        transparent={true}
        visible={firstModalVisible}
        onRequestClose={() => {
          setFirstModalVisible(false);
          slideAnim.setValue(300);
        }}
        onDismiss={() => {
          slideAnim.setValue(300);
        }}
      >
        <KeyboardAvoidingView
          style={styles.popupBackground}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.popupBackground}>
            <Animated.View
              style={[
                styles.cardContainer,
                { transform: [{ translateY: slideAnim }] },
              ]}
            >
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setFirstModalVisible(false)}
              >
                <Ionicons name="close" size={20} color="#000" />
              </TouchableOpacity>
              <View style={styles.container1}>
                {renderInputField("Full name", fullName, setFullName, "default")}
                {renderInputField("E-mail", email, setEmail, "email-address")}
                {renderInputField("Phone Number", phoneNumber, setPhoneNumber, "phone-pad")}
                <View style={styles.buttonWrapper}>
                  <TouchableOpacity
                    onPress={handleSave}
                    style={[styles.button, isProcessingPayment && styles.buttonDisabled]}
                    activeOpacity={0.8}
                    disabled={isProcessingPayment}
                  >
                    <Text style={styles.buttonText}>
                      {isProcessingPayment ? "Processing..." : "Pay Now"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </View>

          {successPopupVisible && (
            <TouchableOpacity
              style={styles.absoluteCenter}
              activeOpacity={1}
              onPress={handleCloseSuccessPopup}
            >
              <View style={styles.successCardContainer}>
                <Image
                  source={AppImages.PATTERN}
                  style={styles.backgroundIMG}
                />
                <Image
                  source={AppImages.ORERSUCCESS}
                  style={styles.successIMG}
                />
                <Text style={styles.successMessage}>Order Placed!</Text>
                <View style={styles.orderInfo}>
                  <Text style={styles.thankYouText}>
                    Thanks for ordering from
                  </Text>
                  <Text style={styles.plentiText}>Plenti</Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
        </KeyboardAvoidingView>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  popupBackground: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  cardContainer: {
    width: "100%",
    backgroundColor: "#FFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    marginBottom: 10,
  },
  closeButton: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  container1: {
    padding: 16,
    gap: 24,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    color: "#9796A1",
    marginLeft: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: "#EEEEEE",
    borderRadius: 12,
    padding: 20,
    fontSize: 16,
    color: "#000",
  },
  activeInput: {
    borderColor: "#5F22D9",
  },
  buttonWrapper: {
    alignItems: "center",
  },
  button: {
    backgroundColor: "#6C5CE7",
    width: 248,
    padding: 22,
    borderRadius: 30,
    alignItems: "center",
    shadowColor: "#FFB4B4",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  buttonDisabled: {
    backgroundColor: "#ccc",
    opacity: 0.7,
  },
  absoluteCenter: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  successCardContainer: {
    width: "90%",
    maxWidth: 400,
    backgroundColor: "#FFF",
    borderRadius: 20,
    padding: 20,
    alignItems: "center",
    elevation: 10,
    shadowColor: "#000",
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },
  backgroundIMG: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    borderRadius: 20,
    resizeMode: "cover",
  },
  successIMG: {
    width: 150,
    height: 150,
    marginVertical: 20,
    resizeMode: "contain",
  },
  successMessage: {
    fontSize: 26,
    color: "#6B50F6",
    marginVertical: 10,
  },
  orderInfo: {
    flexDirection: "row",
    gap: 5,
    alignItems: "center",
  },
  thankYouText: {
    fontSize: 16,
  },
  plentiText: {
    color: "#6B50F6",
    fontSize: 16,
  },
});

export default PersonalDetailsPopup;
